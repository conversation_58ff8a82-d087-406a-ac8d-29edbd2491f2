import cv2
import numpy as np
import time
import os
import keyboard
# from pymouse_pyhook3 import PyMouse, PyMouseEvent

matrix_down = np.array([348.70818667, 0., 602.1629,
                    0., 349.2978, 450.6367,
                    0., 0., 1.]).reshape(3, 3)
dist1_down = np.array([-0.07389198, 0.00344881, 0.00030814, 0.00002226, 0., 0., 0., 0., 0.])[:-1].reshape(1, 8)

matrix_up = np.array([
    353.2221, 0., 621.37822,
    0., 351.45362, 471.21843,
    0., 0., 1.
    ]).reshape(3, 3)
dist_up = np.array([
    -0.075442, 0.003937, 0.000871, -0.000998, 0., 0., 0., 0., 0.
    ])[:-1].reshape(1, 8)

deboot_path = '/home/<USER>/zgt/deebot_aiviewer/img_save/ball_down'
qinboot_path = '/home/<USER>/zgt/deebot_aiviewer/img_save/ball_up'



def video_demo():
    capture_down = cv2.VideoCapture(1)#0为电脑内置摄像头
    capture_down.set(3, 1280)
    capture_down.set(4, 960)
    capture_up = cv2.VideoCapture(2)
    capture_up.set(3, 1280)
    capture_up.set(4, 960)
    flg = 0
    while(True):
        # time.sleep(5)
        ret, frame_down = capture_down.read()#摄像头读取,ret为是否成功打开摄像头,true,false。 frame为视频的每一帧图像
        # ret2, frame_up = capture_up.read()
        # 矫畸变
        frame_down = cv2.undistort(frame_down, matrix_down, dist1_down, None, matrix_down)
        # frame_up = cv2.undistort(frame_up, matrix_up, dist_up, None, matrix_up)
        # cv2.imshow("down", frame_down)
        # cv2.imshow("up", frame_up)
        c = cv2.waitKey(50)
        if c == 27:
            break
        # if flg != 10:
        #     flg += 1
        #     continue

        # flg = 0
        jpg_time = time.time()
        jpg_name = str(jpg_time).replace('.', '_') + '.jpg'
        print('save: ', jpg_name)
        deboot_save_path = os.path.join(deboot_path, jpg_name)
        qinboot_save_path = os.path.join(qinboot_path, jpg_name)
        # cv2.imwrite(qinboot_save_path, frame_up)
        flg += 1
        # if flg % 10 == 0:
        # cv2.imwrite(deboot_save_path, frame_down)
        cv2.imshow("down", frame_down)

        # if flg > 70:
        #     return
video_demo()
cv2.destroyAllWindows()

