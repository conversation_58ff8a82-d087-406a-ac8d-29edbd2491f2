{"pid_file": "/tmp/medusa.pid", "lock_port": 2012, "plugins_dir": "/usr/lib/medusa", "elements": [{"class": "LOG", "name": "log0", "level": "log_local0", "enable_dump_trace": 1, "program_name": "/usr/bin/medusa", "addr2line": "/usr/bin/addr2line", "enable_log_to_file": 1, "log_file": "/tmp/log/md.log", "log_file_size_byte": 1048576, "log_hook": "/usr/bin/log_hook.sh"}, {"class": "CMD", "name": "cmd0", "unix_socket_path": "/tmp/mds_cmd.sock", "max_connections": 10}, {"class": "BufCmdToJsonCmd", "name": "b2j"}, {"class": "SYS", "name": "sys0", "enable_mcu_version": 0, "pid_bin_file": "/dev/ubi2_0", "pid_bin_file_offset": 0, "pid_bin_file_leb_num": 1, "ubi_dev_leb_size": 253952, "default_country_language_offset": 0, "default_country_language_leb_num": 2, "motor_parameters_offset": 4096, "motor_parameters_leb_num": 2, "eco_tiot_parameters_offset": 8192, "eco_tiot_parameters_leb_num": 2, "feiyan_tiot_parameters_offset": 9216, "feiyan_tiot_parameters_leb_num": 2, "joylink_tiot_parameters_offset": 10240, "joylink_tiot_parameters_leb_num": 2, "camera_inner_param_offset": 12360, "camera_inner_param_leb_num": 2, "camera_outer_param_offset": 14336, "camera_outer_param_leb_num": 2, "pid_bin_file_readonly": 1, "lds_config_file_path": "/tmp/config.bin", "default_lb_srv": "lb.ecouser.net", "enable_soft_mac": 0, "sys_hook": "/etc/conf/medusa/sys.sh", "enable_arm_fw": 1, "arm_fw_hook": "/etc/fw.manifest", "enable_push_sys_info": 0, "enable_debug_mode": 0, "product_key": "a1APxIBQaT1", "product_secret": "chkhdi24XbprHC7y", "debug_mode": {"sn": "E0000000018150510333", "mid": "E0000000018150510333", "eco_iot": {"lb_srv": "mq.ecouser.net", "did": "2016554c-e636-47b4-a729-8d2659dcaf9a", "password": "GUFAzXJ6", "type": "db87rf"}, "dj_joylink_iot": {"uuid": "291EFC", "mac": "1234567890AB", "key": "hsbihKAHSU8ADHBCOnjshIHUI"}, "ali_feiyan_iot": {"product_key": "a1APxIBQaT1", "product_secret": "chkhdi24XbprHC7y", "device_name": "TfsRfYf9uBJtJcTPJKvc", "device_secret": "52LV9zJ8cmiyU5jR97cX30RwZlOnm3M7"}, "wifiMac": "54:4e:90:c2:77:17"}}, {"class": "FW", "name": "fw", "support_auto_ota": 1, "fw_config_path": "/data/config/medusa/enableAutoOta.conf", "fwVerPath": "/etc/fw.manifest", "fw_fail_reboot": "/etc/conf/medusa/fw_reboot.sh", "fw_file_name": "/data/fw.bin", "do_update_hook": "/usr/bin/fw_upgrade.sh", "do_cut_hook": "/usr/bin/fw_cut.sh", "download_fw_hook": "/etc/conf/medusa/download_fw_hook_v3.sh", "PlaySound_hook": "/etc/conf/medusa/play_sound.sh", "slOtaHook": "/etc/conf/medusa/sl_ota_hook.sh", "language_in_use_path": "/data/log/language_in_use", "sound_num_in_upgrading": 90, "sound_num_upgrade_success": 56, "sound_num_upgrade_fail": 76, "domain_china": "portal.ecouser.net", "domain_others": "portal-ww.ecouser.net"}, {"class": "MQTT", "name": "mqtt", "mqttAclEnable": 0, "luaLibPath": "/usr/lib/medusa/shell/lua"}, {"class": "BUMBEE", "name": "bumbee", "sys_elem": "sys0", "web_host": "127.0.0.1", "web_port": 8888, "web_enable": 0, "hook_script": "/etc/wifi/bumbee_hook.sh", "rsp_delete_timeout": 60, "rsp_warn_timeout": 30, "remote_shell_store_dir": "/tmp", "rwCfg": "/data/config/medusa/rwCfg.json", "packet_debug": "0", "dtcfg_group_num": 249, "enable_debug_mode": "0"}, {"class": "TIME", "name": "time0", "timezone_path": "/data/config/medusa/localtime", "time_hook": "/etc/conf/medusa/set_time_help.sh"}, {"class": "AUDIO", "name": "audio0", "audio_config_path": "/data/config/medusa/audio.conf", "get_audio_help_hook": "/etc/conf/medusa/get_audio_help.sh", "down_audio_hook": "/usr/bin/down_audio_hook.sh", "prsonalized_voice_path": "/data/music/", "language_in_use_path": "/data/log/language_in_use", "language_in_use_id_path": "/data/log/language_in_use_id", "music_dir": "/media/music/", "partition_path": "/dev/ubi2_0", "ubi_dev_leb_size": 253952, "default_language_offset": 0, "default_language_leb_num": 2, "default_others_speaker_volume": 255, "default_en_speaker_volume": 255, "sound_num_i_am_here": 30, "plug_mii_name": "mmi930"}, {"class": "RosNode", "name": "rosnode", "crc32": "1295764014", "bigdata": {"interval": 60, "seq": 15}, "sta_plug_name": "station0"}, {"class": "DEV_STATUS", "name": "DevStatus0", "xmpp_server_status_hook": "/etc/conf/medusa/xmpp_server_status.sh", "charge_status_hook": "/usr/bin/charge_status.sh", "wifi_led_ctl_hook": "/etc/conf/medusa/WiFiledStatusCtl.sh", "disable_wifi_hook": "/etc/conf/medusa/disable_wifi.sh", "SCCFGResHook": "/etc/wifi/SCCFGResHook.sh", "StationHook": "/etc/wifi/station_ap.sh", "StationApConf": "/data/config/medusa/station.conf", "network_plug_name": "nt0", "IotRegisterMark": "/data/config/registered.mark"}, {"class": "NETWORK", "name": "nt0", "iot_plug_name": "mqtt", "dev_plug_name": "DevStatus0", "netinfoPath": "/tmp/netInfo_note", "netmon_socket_path": "/tmp/netmon.sock"}, {"class": "UDP", "name": "Udp0", "is_server": 1, "listen_ip": "0.0.0.0", "listen_port": 2375}, {"class": "STATION", "name": "station0", "rosnode_plug_name": "rosnode", "devStatus_plug_name": "DevStatus0"}, {"class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "log2file": 1, "method_lib_path": "/usr/lib/WHmethods", "fwVerPath": "/etc/fw.manifest", "filter": {"pos_accuracy": "200.00", "angle_accuracy": "20.00", "area_accuracy": "1", "tim_accuracy": "60"}}, {"class": "SWITCH", "name": "switch", "function_table": "/etc/conf/func_table.conf", "devName": "DevStatus0", "iot": [{"name": "bumbee", "All": 1, "functions": ["device_info", "water_box", "clean_logic", "map_info", "voice", "device_status", "clean_status", "bigdata", "multi_map", "ota"]}, {"name": "convert_feiyan", "All": 1}]}, {"class": "PROTOCOL_CONVERT_FEIYAN", "name": "convert_feiyan", "dest": "linkkit", "src": "switch", "isRosNode": 1, "method_path": "/usr/lib/FeiyanCvtMethod/libFeiyanCvtMethod.so", "rwCfg": "/data/config/medusa/rwCfg.json"}, {"class": "VIDEO_GENERATOR", "name": "camera0", "camera_type": "sc1330t", "gdc_path": "/data/gdc.bin", "capture_dir": "/tmp", "chn0": {"gdc": 1, "crop": 0, "width": 1280, "height": 720}, "chn1": {"gdc": 0, "crop": 1, "x": 160, "y": 160, "width": 1024, "height": 700}, "chn2": {"gdc": 0, "crop": 0, "x": 0, "y": 0, "width": 1280, "height": 960}, "vin_width": 1280, "vin_height": 960}, {"class": "X3_BPU", "name": "bpu", "bpu_log": 0, "cam_inner_para": "/data/inner.json", "cam_outside_para": "/data/AI_para.txt", "bpu0_threads": 5, "bpu1_threads": 1, "fps": 30, "model_dir": "/model", "model_files": [{"name": "net-en.bin", "id": 0, "times": 6, "core": 0, "limit": 17, "gdc": 1}, {"name": "net-en1.bin", "id": 1, "times": 4, "core": 0, "limit": 14, "gdc": 1}, {"name": "net-en2.bin", "id": 2, "times": 3, "core": 0, "limit": 19, "gdc": 0}, {"name": "net-en3.bin", "id": 3, "times": 3, "core": 1, "limit": 85, "gdc": 1}, {"name": "net-en4.bin", "id": 4, "times": 3, "core": 0, "limit": 13, "gdc": 0}, {"name": "net-en5.bin", "id": 5, "times": 6, "core": 0, "limit": 6, "gdc": 1}], "model_custom": "/data/model_custom.json"}, {"class": "MEDIA_CONTROL", "name": "mediactl", "plug_tiot_name": "bumbee", "process_pic_script": "/etc/conf/medusa/process_picture.sh"}, {"class": "MCUOTA", "name": "m<PERSON>ota", "mcu_ota_en": 1, "to_name": "test", "ttyDev": "/dev/ttyS4"}, {"class": "MCUOTA", "name": "slota", "mcu_ota_en": 1, "to_name": "test", "ttyDev": "/dev/ttyS3"}, {"class": "HB_VENC", "name": "venc", "width": 1280, "height": 960, "gop": 15, "fps": 15, "bps_720p": 1024, "bps_480p": 512}, {"class": "LVISON", "name": "lvision", "plug_sys_name": "sys0", "plug_mediactl_name": "mediactl", "plug_dev_status_name": "DevStatus0", "beep_sound_num": 17, "conf": "/data/config/medusa/lvision.conf", "microphone": {"rate": 8000}, "speaker": {"rate": 8000, "volume": 120}}, {"class": "LINK", "name": "linkkit", "plug_sys_name": "sys0", "plug_dev_status_name": "DevStatus0"}, {"class": "LIVE_PWD", "name": "live_pwd", "conf": "/data/config/medusa/password.conf", "press_by_cmd": 0}, {"class": "ObjectDetect", "name": "objectDetector", "distablePath": "/data/distance_table"}, {"class": "INPUT_EVENT", "name": "reset", "autokey_hook": "/etc/conf/medusa/autokey_hook.sh"}], "chains": [["cmd0", "cmd0"], ["cmd0", "b2j"], ["cmd0", "fw"], ["b2j", "DevStatus0"], ["bumbee", "DevStatus0"], ["DevStatus0", "bumbee"], ["sys0", "DevStatus0"], ["mqtt", "DevStatus0"], ["DevStatus0", "mqtt"], ["rosnode", "DevStatus0"], ["rosnode", "station0"], ["b2j", "station0"], ["nt0", "DevStatus0"], ["DevStatus0", "nt0"], ["Udp0", "station0"], ["station0", "Udp0"], ["station0", "DevStatus0"], ["station0", "rosnode"], ["station0", "time0"], ["time0", "station0"], ["b2j", "nt0"], ["nt0", "mqtt"], ["mqtt", "nt0"], ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "nt0"], ["b2j", "rosnode"], ["b2j", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "rosnode"], ["rosnode", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], ["rosnode", "audio0"], ["rosnode", "time0"], ["rosnode", "reset"], ["time0", "rosnode"], ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "switch"], ["switch", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], ["switch", "DevStatus0"], ["b2j", "audio0"], ["mqtt", "bumbee"], ["bumbee", "mqtt"], ["b2j", "bumbee"], ["bumbee", "sys0"], ["sys0", "bumbee"], ["bumbee", "audio0"], ["audio0", "bumbee"], ["bumbee", "switch"], ["switch", "bumbee"], ["b2j", "sys0"], ["fw", "audio0"], ["b2j", "fw"], ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "fw"], ["fw", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], ["fw", "rosnode"], ["rosnode", "fw"], ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "DevStatus0"], ["DevStatus0", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "audio0"], ["audio0", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], ["b2j", "time0"], ["bumbee", "time0"], ["time0", "bumbee"], ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "time0"], ["b2j", "m<PERSON>ota"], ["m<PERSON>ota", "fw"], ["b2j", "slota"], ["slota", "fw"], ["convert_feiyan", "switch"], ["switch", "convert_feiyan"], ["linkkit", "convert_feiyan"], ["convert_feiyan", "linkkit"], ["b2j", "convert_feiyan"], ["convert_feiyan", "sys0"], ["linkkit", "sys0"], ["linkkit", "DevStatus0"], ["linkkit", "time0"], ["b2j", "linkkit"], ["b2j", "camera0"], ["b2j", "mediactl"], ["mediactl", "camera0"], ["b2j", "bpu"], ["mediactl", "bpu"], ["camera0", "bpu"], ["bpu", "camera0"], ["bpu", "objectDetector"], ["b2j", "objectDetector"], ["b2j", "camera0"], ["camera0", "venc"], ["b2j", "venc"], ["lvision", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "lvision"], ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "mediactl"], ["mediactl", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], ["venc", "lvision"], ["lvision", "venc"], ["b2j", "linkkit"], ["b2j", "lvision"], ["lvision", "sys0"], ["linkkit", "lvision"], ["lvision", "mediactl"], ["mediactl", "lvision"], ["lvision", "linkkit"], ["mediactl", "venc"], ["lvision", "audio0"], ["b2j", "live_pwd"], ["live_pwd", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], ["live_pwd", "rosnode"], ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "live_pwd"]]}