import os
import sys
import cv2
import json

label_map = {1: 'ball',
             2: 'charge',
             3: 'cloth',
             4: 'rug',
             5: 'shoe',
             6: 'wire',
             7: 'bowl',
             8: 'rail',
             9: 'uchair',
             10: 'iron_bar',
             11: 'iron_bar'}

color_map = {
    1: (255, 0, 0),
    2: (0, 255, 0),
    3: (0, 0, 255),
    4: (100, 100, 255),
    5: (100, 233, 100),
    6: (255, 100, 100),
    7: (240, 90, 100),
    8: (100, 255, 70),
    9: (100, 10, 255),
    10: (255, 255, 10),
    11: (255, 20, 40),
}


files = []
srcdir = r'/home/<USER>/zgt/deebot_aiviewer/images/2022_01_23_17_34_39_909112'

videowriter = cv2.VideoWriter("/home/<USER>/zgt/deebot_aiviewer/images/test1735_6fps.mp4", cv2.VideoWriter_fourcc('M', 'J', 'P', 'G'), 6, (1280, 720))

for file in os.listdir(srcdir):
    if file.endswith('json'):
        continue
    else:
        files.append(file)
files.sort()
for file in files:
    filename = file.split('.')[0]
    if os.path.exists(os.path.join(srcdir, file)) and os.path.exists(os.path.join(srcdir, filename+'.jpg')):
        img = cv2.imread(os.path.join(srcdir, file))

        json_r = json.load(open(os.path.join(srcdir, filename+'.json')))
        # print(filename)
        if 'rets' in json_r.keys():
            print(filename)
            modelid = json_r['model_id']
            for ret in json_r['rets']:
                xmin = int(ret['xmin'])
                xmax = int(ret['xmax'])
                ymin = int(ret['ymin'])
                ymax = int(ret['ymax'])
                score = ret['score']
                label = ret['class']
                if score > 0.8:
                    clr = color_map.get(ret['class'], (255, 255, 255))
                    cv2.rectangle(img, (xmin, ymin), (xmax, ymax), clr, thickness=1)
                    # show txt
                    # txt = 'modelid {}-{}:{:.3f}-({},{})({},{})'.format(modelid,label_map.get(ret['class'], 'unknown'), ret['score'],xmin,ymin,xmax,ymax)
                    txt = '{}:{:.3f}'.format(label_map.get(ret['class'], 'unknown'), ret['score'])
                    font = cv2.FONT_HERSHEY_COMPLEX
                    cat_size = cv2.getTextSize(txt, font, 0.5, 2)[0]
                    cv2.rectangle(img, (xmin, ymin + cat_size[1] + 1), (xmin + cat_size[0] + cat_size[1], ymin + 1),
                                  clr, -1)
                    cv2.putText(img, txt, (xmin, ymin + cat_size[1]), font, 0.5, (0, 0, 0), thickness=1,
                                lineType=cv2.LINE_AA)
        cv2.imshow('img', img)
        videowriter.write(img)
        cv2.waitKey(10)
