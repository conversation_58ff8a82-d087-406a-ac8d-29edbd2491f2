"""
模拟的地宝socket客户端，发送数据用于测试
Create by <PERSON>qi.Lv
2019.11.04
"""

import socket
import sys
import time

s = socket.socket()
s.connect(("127.0.0.1", 8888))
with open("/home/<USER>/Pictures/20191018-手板/充电座/0/2019_10_18_06_53_24_797.jpg", "rb") as f:
    img = f.read()
with open("/home/<USER>/Pictures/20191018-手板/布料/16团/0/2019_10_18_03_21_32_227.jpg", "rb") as f:
    img2 = f.read()
with open("/home/<USER>/Pictures/20191018-手板/布料/57团/0/2019_10_18_05_52_52_254.jpg", "rb") as f:
    img3 = f.read()

img_list = [img, img2, img3]

json_file = open('/home/<USER>/Pictures/20191018-手板/充电座/0/2019_10_18_06_53_24_797.json', 'rb')
json_data = json_file.read()

# print(len(img))
i = 0
while i < 100:
    idx = i % 3
    s.send(b'picstart ')  # 开始标志位后面要多一个字节
    s.send(img_list[idx])
    s.send(b'picend')
    s.send(b'resultstart ')
    s.send(json_data)
    s.send(b'resultsend')
    print('send', i)
    i += 1
    time.sleep(1)
s.close()
