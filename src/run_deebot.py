import os
import sys

SSID = "HUAWEIMATE"
KEY = "12342234"
PC_IP = "**************"

order = """
netmon_ctl -s /tmp/netmon.sock -j "{\\"connect\\":\\"wifi_connect\\",\\"ssid\\":\\"SSID\\",\\"passphrase\\":\\"KEY\\"}"
mdsctl camera0 '{"origin_on":1}'
mdsctl objectDetect  '{"todo":"odTest" , "cmd": "start","ip":"PC_IP","type":0,"port": 8888}'
""".replace("SSID", SSID).replace("KEY", KEY).replace("PC_IP", PC_IP)
with open("./order.sh", "w") as f:
    f.write(order)

input("运行AIViewer.py,点击connect后按任意键继续")
os.system("adb push ./order.sh /tmp/")
os.system("adb shell chmod 777 /tmp/order.sh")
os.system("adb shell sh /tmp/order.sh")
