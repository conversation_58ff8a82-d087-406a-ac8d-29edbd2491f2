mdsctl rk_drm '{"todo":"switch_log","cmd":"open"}'
mount -o bind /data/preisp.rkl /lib/firmware/rk1608.rkl
mdsctl objectDetector '{"todo":"odTest","cmd":"start","ip":"*************","type":1,"port":8888}'
mdsctl objectDetector '{"todo":"odTest","cmd":"start","ip":"************","type":1,"port":8888}'
mdsctl objectDetector '{"todo":"odTest","cmd":"start","ip":"**************","type":1,"port":8888}'
mdsctl objectDetector '{"todo":"odTest","cmd":"start","ip":"**************","type":1,"port":8888}'
mdsctl objectDetector '{"todo":"odTest","cmd":"start","ip":"************","type":1,"port":8888}'
mdsctl objectDetector '{"todo":"odTest","cmd":"start","ip":"**************","type":1,"port":8888}'
mdsctl objectDetector '{"todo":"odTest","cmd":"start","ip":"**************","type":1,"port":8888}'
mdsctl objectDetector '{"todo":"odTest","cmd":"start","ip":"**************","type":1,"port":8888}'

mdsctl objectDetector '{"todo":"odTest","cmd":"start","ip":"*************","type":1,"port":8888}'

mdsctl objectDetector '{"todo":"odTest","cmd":"start","ip":"***********","type":1,"port":8888}'


"ssid\":\"rainman\",\"passphrase\":\"12345600\"}"
netmon_ctl -s /tmp/netmon.sock -j "{\"connect\":\"wifi_connect\",\"ssid\":\"youngxd\",\"passphrase\":\"yxddwifi\"}"

/etc/rc.d/medusa.sh

killall -9 medusa
killall -9 deebot
 # T10配网
netmon_ctl -s /tmp/netmon.sock -j "{\"connect\":\"wifi_connect\",\"ssid\":\"Irish\",\"passphrase\":\"asddsa123\"}"
netmon_ctl -s /tmp/netmon.sock -j "{\"connect\":\"wifi_connect\",\"ssid\":\"None\",\"passphrase\":\"111111111\"}"
netmon_ctl -s /tmp/netmon.sock -j "{\"connect\":\"wifi_connect\",\"ssid\":\"zzf123\",\"passphrase\":\"12345678\"}"
netmon_ctl -s /tmp/netmon.sock -j "{\"connect\":\"wifi_connect\",\"ssid\":\"123\",\"passphrase\":\"123456789\"}"
netmon_ctl -s /tmp/netmon.sock -j "{\"connect\":\"wifi_connect\",\"ssid\":\"RD_Test\",\"passphrase\":\"Eco#2022\"}"
netmon_ctl -s /tmp/netmon.sock -j "{\"connect\":\"wifi_connect\",\"ssid\":\"HUAWEI-B315-424B\",\"passphrase\":\"81642643\"}"
netmon_ctl -s /tmp/netmon.sock -j "{\"connect\":\"wifi_connect\",\"ssid\":\"iQOO Z1x\",\"passphrase\":\"gczxy132707\"}"
netmon_ctl -s /tmp/netmon.sock -j "{\"connect\":\"wifi_connect\",\"ssid\":\"xiaoyue\",\"passphrase\":\"18896557134\"}"
netmon_ctl -s /tmp/netmon.sock -j "{\"connect\":\"wifi_connect\",\"ssid\":\"emmmmmm\",\"passphrase\":\"wzx12345678\"}"

# T10 启动摄像头
mdsctl camera0 '{"origin_on":1}'

mdsctl camera0 '{"todo":"get_hdr"}'
mdsctl camera0 '{"todo":"set_hdr", "hdr":1}'
mdsctl camera0 '{"todo":"set_hdr", "hdr":0}'

# t10 截图
mdsctl camera0 '{"todo":"capture_picture","skip_frames":5}':q!

# t10 启动ai
mdsctl mediactl '{"todo":"start_ai"}'
mdsctl mediactl '{"todo":"stop_ai"}'
# data下deebot_x3.conf为配置文件，定位到"class":"X3_BPU"，为模型的配置 "class":"VIDEO_GENERATOR",为镜头配置
netmon_ctl -s /tmp/netmon.sock -j "{\"connect\":\"wifi_connect\",\

tar -czvf backup.tar.gz backup/

cat /tmp/log/md_log.* | grep timestamp2

killall -9 medusa

/data/autostart/deebot.sh start
/data/autostart/medusa.sh start
cd /data/ros_ws/
./detect.sh 100m
rm -rf *


adb pull /tmp/backup.tar.gz .
adb pull /tmp/log/md.log_0 .

/data/ros_ws/detect.sh 50

#查看机器版本
tail /etc/fw.manifest

#查看模型检测结果
tail -f /tmp/log/md.log_* | grep "model id:0" -A 10 或者 chrisyoung
tail -f /tmp/log/md.log_* | grep  chrisyoung
#查看触发的命令
tail -f /tmp/log/md.log_* | grep "ME"
#查看bpu占用情况
hrut_somstctdetecatus -d 1 -n 1000
#= 统计模型当前的配置帧数
mdsctl bpu '{"todo":"bpu_statistic"}'

# 查看medusa进程号
ps -aux |grep medusa
#　查看进程的对应库的启动路径
cat /proc/2397/maps | grep libpixel

#数据采集，机器测试
#采集配置文件
/data/config/.data_filter/aiImage
#保存路径
/tmp/data_filter/aiImage
#设置采集类别
/data/dxai_node.json
#系统配置路径
/usr/share/eros_node_data_filter
#查看用户改进计划是否打开,不打开无法采图！！！！！！
grep user /tmp/log/aiimage_data_filter.log.000

#查看电量
mdsctl rosnode '{"todo":"rtctl","cmd":"getBatteryInfo"}'

重启机器，ps –ef | grep deebot 看下deebot进程和medusa进程是不是都在
tail -f /tmp/log/aiimage_data_filter.log.00*
grep "sync data" /tmp/log/aiimage_data_filter.log.00*
tail -f /tmp/log/aiimage_data_filter.log.00* | grep "save\|Save"
medusa 打印日志至终端
MALLOC_CHECK_=2 medusa -f /data/medusa/deebot_x3.conf &
取消上传
cp /usr/bin/eros_data_upload.sh /data/
eros_data_upload.sh
# [[ $E_DEL -eq 1 ]] && remove_origin_data $E_PATH $E_UNAME






设置不同模型帧率
mdsctl bpu '{"todo":"update_model_fps","cmds":[{"id":0,"times":0},{"id":1,"times":0},{"id":2,"times":0},{"id":3,"times":0},{"id":4,"times":0},{"id":5,"times":0},{"id":6,"times":20}]}'

mdsctl bpu '{"todo":"update_model_fps","cmds":[{"id":0,"times":6},{"id":1,"times":0},{"id":2,"times":0},{"id":3,"times":0},{"id":4,"times":0},{"id":5,"times":0},{"id":6,"times":0},{"id":8,"times":0}]}'



启动工作后，设置对应的id计算帧率
mdsctl bpu '{"todo":"update_model_fps","cmds":[{"id":0,"times":6},{"id":1,"times":0},{"id":2,"times":0},{"id":3,"times":0},{"id":4,"times":0},{"id":5,"times":0},{"id":6,"times":0},{"id":7,"times":0},{"id":8,"times":0},{"id":9,"times":0},{"id":11,"times":0},{"id":15,"times":0},{"id":16,"times":6}]}'

mdsctl bpu '{"todo":"update_model_fps","cmds":[{"id":0,"times":0},{"id":1,"times":0},{"id":2,"times":0},{"id":3,"times":0},{"id":4,"times":0},{"id":5,"times":0},{"id":6,"times":0},{"id":7,"times":0},{"id":8,"times":0},{"id":16,"times":6},{"id":1,"times":6}]}'

mdsctl bpu '{ "todo":"update_model_fps","cmds":[{"id":0,"times":6}]}

\

mdsctl bpu '{"todo":"update_model_fps","cmds":[{"id":0,"times":0},{"id":1,"times":0},{"id":2,"times":0},{"id":3,"times":0},{"id":4,"times":0},{"id":5,"times":0},{"id":6,"times":0},{"id":7,"times":0},{"id":8,"times":0},{"id":11,"times":0},{"id":15,"times":0},{"id":16,"times":3},{"id":17,"times":0}]}'

