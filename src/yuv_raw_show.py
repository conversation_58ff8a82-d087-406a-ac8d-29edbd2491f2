
import numpy as np
import cv2
import os
import struct
import argparse

# open_path = '/media/zhangdezheng/new_volume/RAW_data暗光图像/欧菲光raw/my/source_BGGR10_1.raw'


def read_raw_deebot(open_path):
    f = open(open_path, 'rb')
    n = f.read()
    bit_ = []
    for ni in n:
        bit_.append(bin(ni).replace('0b','').zfill(8))
    pixl_list=[]
    for i in range(int(len(bit_)/5)):
        temp_bit = bit_[i*5:i*5+5]
        temp_bit[3] = temp_bit[3]+temp_bit[-1][0:2]
        temp_bit[2] = temp_bit[2]+temp_bit[-1][2:4]
        temp_bit[1] = temp_bit[1]+temp_bit[-1][4:6]
        temp_bit[0] = temp_bit[0]+temp_bit[-1][6:8]
        pixl_list.append(int(temp_bit[0], 2))
        pixl_list.append(int(temp_bit[1], 2))
        pixl_list.append(int(temp_bit[2], 2))
        pixl_list.append(int(temp_bit[3], 2))
        # print(temp_bit)
    pixl_array = np.array(pixl_list)
    bayer_pixl_array = pixl_array.reshape((960,1280))
    bayer_pixl_array = np.floor(bayer_pixl_array/4)
    return bayer_pixl_array

def BGGR_rgb(img):
    # show = np.floor(img)
    show = img
    rgb_pic = np.zeros([960,1280,3])
    for x in range(1,959):
        for y in range(1, 1279):
            if (y==1 | y==959 | x==1 | x==1279):
                rgb_pic[y,x,:] = 0
            elif x%2==1 & y%2==1:
                rgb_pic[x,y,0] = (show[x-1,y-1]+show[x+1,y-1]+show[x-1,y+1]+show[x+1,y+1])/4
                rgb_pic[x,y,1] = (show[x,y-1]+show[x,y+1]+show[x-1,y]+show[x+1,y])/4
                rgb_pic[x,y,2] = show[x,y]
            elif x%2==0 & y%2==0:
                rgb_pic[x,y,0] = show[x,y]
                rgb_pic[x,y,1] = (show[x,y-1] + show[x,y+1] + show[x-1,y] + show[x+1,y])/4
                rgb_pic[x,y,2] = (show[x-1,y-1] + show[x+1,y-1] + show[x-1,y+1] + show[x+1,y+1])/4
            elif x%2==1:
                rgb_pic[x,y,0] = (show[x-1,y] + show[x+1,y])/2
                rgb_pic[x,y,1] = show[x,y]
                rgb_pic[x,y,2] = (show[x,y-1] + show[x,y+1])/2
            else:
                rgb_pic[x,y,0] = (show[x,y-1] + show[x,y+1])/2
                rgb_pic[x,y,1] = show[x,y]
                rgb_pic[x,y,2] = (show[x-1,y] + show[x+1,y])/2
    return np.uint8(rgb_pic)

def conv_rgb(img):
    n, stride,p=2,2,1
    kernel_b = np.array([[1,0],[0,0]])
    kernel_g = np.array([[0,0.5],[0.5,0]])
    kernel_r = np.array([[0,0],[0,1]])
    h, w = img.shape

    img = np.pad(img,((1,1),(1,1)),'constant',constant_values=0)
    res_h = ((h+2*p-n)//stride)+1 #卷积边长计算公式：((n+2*p-k)/stride)+1
    res_w = ((w+2*p-n)//stride)+1
    res_b = np.zeros([res_h, res_w])
    res_g = np.zeros([res_h, res_w])
    res_r = np.zeros([res_h, res_w])
    # print(res)
    for i in range(res_h):
        for j in range(res_w):
            temp = img[i*stride:i*stride+n , j*stride:j*stride+n]
            # print((i*stride,i*stride+n,j*stride,j*stride+n)) #打印检查卷积核每次卷积的位置对否
            temp_b = np.multiply(kernel_b, temp)
            res_b[i][j] = temp_b.sum()
            temp_g = np.multiply(kernel_g, temp)
            res_g[i][j] = temp_g.sum()
            temp_r = np.multiply(kernel_r, temp)
            res_r[i][j] = temp_r.sum()
    conv_arr = np.concatenate((res_r[:,:,np.newaxis],res_g[:,:,np.newaxis],res_b[:,:,np.newaxis]),axis=2)
    return np.uint8(conv_arr)

def yuv2bgr(filename, height, width):
    fp = open(filename, 'rb')
    h_h = height // 2
    h_w = width // 2
    Yt = np.zeros(shape=(height, width), dtype=np.uint8 , order='C')
    Ut = np.zeros(shape=(h_h, h_w), dtype=np.uint8, order='C')
    Vt = np.zeros(shape=(h_h, h_w), dtype=np.uint8, order='C')

    for m in range(height):
        for n in range(width):
            Yt[m, n] = ord(fp.read(1))
    for m in range(h_h):
        for n in range(h_w):
            Ut[m, n] = ord(fp.read(1))
    for m in range(h_h):
        for n in range(h_w):
            Vt[m, n] = ord(fp.read(1))

    img = np.concatenate((Yt.reshape(-1), Ut.reshape(-1), Vt.reshape(-1)))
    img = img.reshape((height * 3 // 2, width))

    bgr_img = cv2.cvtColor(img, cv2.COLOR_YUV2BGR_NV12)
    cv2.imwrite(filename[:-4]+'.png', bgr_img)

    fp.close()
    return None

if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument("--dir", default='/media/robot/aaa/raw/temp', type=str)
    args = parser.parse_args()
    lists = []
    try:
        lists = os.listdir(args.dir)
    except Exception as e :
        print("%s does not exists"%args.dir)

    if len(lists)>0:
        lists = [l for l in lists if ".yuv" in l]
        for item in lists:
            print(item)
            yuv2bgr(filename=os.path.join(args.dir, item), height=960, width=1280)


    # open_path = '/media/robot/aaa/raw/777/pic_1280_960_1-22346279667.raw'
    # bayer_arr = read_raw_deebot(open_path)
    # # pic = BGGR_rgb(bayer_arr)
    # pic_conv = conv_rgb(bayer_arr)
    #
    # cv2.namedWindow('show', 0)
    # cv2.resizeWindow('show', 960, 1280)
    # # rgb_pic = cv2.rotate(img,cv2.ROTATE_90_CLOCKWISE)
    # cv2.imshow('gray', np.uint8(bayer_arr))
    # # cv2.imshow('BGGR_rgb', pic)
    # cv2.imshow('pic_conv', pic_conv)
    # #
    # cv2.waitKey()
    # cv2.destroyAllWindows()